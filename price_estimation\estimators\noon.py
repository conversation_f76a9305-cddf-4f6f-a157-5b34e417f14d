"""
Noon.com Egypt price estimator.
"""

import logging
import time
from typing import Union

from selenium.webdriver.common.by import By

from .base import BasePriceEstimator, PriceEstimationError
from ..utils.web_scraping import WebScrapingUtils
from ..config.estimation_config import EstimationConfig

logger = logging.getLogger('price_estimation')


class NoonPriceEstimator(BasePriceEstimator):
    """Noon.com Egypt price estimator using Selenium."""

    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Noon.com Egypt using Selenium."""
        import re

        logger.info(f"Starting Noon price estimation for: {name}")

        search_query = f"{name} {description}".replace(" ", "+")
        url = f"https://www.noon.com/egypt-en/search?q={search_query}"

        logger.debug(f"Noon URL: {url}")

        driver = None
        try:
            driver = WebScrapingUtils.get_webdriver()
            driver.get(url)
            time.sleep(3)  # Wait for page to load (<PERSON>on needs more time than Amazon)

            # Find price elements using multiple selectors (based on actual Noon structure)
            price_selectors = [
                # Noon-specific selectors (updated based on real structure)
                "span[data-qa='product-price']",
                "div[data-qa='product-price']",
                "[data-qa='price-current']",
                "[data-qa='price-now']",
                ".currency",
                ".price",
                ".product-price",
                ".priceNow",

                # Generic price selectors
                "[class*='price']",
                "[class*='Price']",
                "span[class*='currency']",
                "div[class*='currency']",

                # Styled components (Noon uses these)
                ".sc-dlfnbm",
                ".sc-fKVqWL",
                ".sc-bdfBwQ",

                # Fallback selectors
                "span:contains('EGP')",
                "div:contains('EGP')",
                "span:contains('ج.م')",
                "div:contains('ج.م')",

                # Very broad selectors
                "span",
                "div"
            ]

            logger.debug(f"Searching for price elements on Noon page")

            # Debug: Let's see what's actually on the page
            try:
                page_title = driver.title
                logger.debug(f"Noon page title: {page_title}")

                # Check if we have any divs at all
                all_divs = driver.find_elements(By.TAG_NAME, "div")
                logger.debug(f"Total divs found on page: {len(all_divs)}")

                # Look for any text that might contain "EGP" or numbers
                page_text = driver.page_source[:2000]  # First 2000 chars
                if "EGP" in page_text or "ج.م" in page_text:
                    logger.debug("Found EGP/ج.م in page source")
                else:
                    logger.debug("No EGP/ج.م found in page source")

            except Exception as e:
                logger.debug(f"Error during page debugging: {str(e)}")

            for selector in price_selectors:
                try:
                    price_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    logger.debug(f"Selector '{selector}' found {len(price_elements)} elements")

                    for price_element in price_elements:
                        price_text = price_element.text.replace(",", "").strip()

                        # For broad selectors (span, div), be more selective
                        if selector in ["span", "div"]:
                            # Only process if text contains EGP or looks like a price
                            if not ("EGP" in price_text or "ج.م" in price_text or re.search(r'\d{3,}', price_text)):
                                continue

                        logger.debug(f"Found price text: '{price_text}'")

                        # Try to extract price using regex patterns
                        price_patterns = [
                            r'(\d+(?:,\d{3})*(?:\.\d{2})?)',  # Basic number pattern
                            r'(\d+(?:,\d{3})*)\s*(?:EGP|ج\.م)',  # Number followed by currency
                            r'(?:EGP|ج\.م)\s*(\d+(?:,\d{3})*)',  # Currency followed by number
                        ]

                        for pattern in price_patterns:
                            matches = re.findall(pattern, price_text)
                            for match in matches:
                                try:
                                    price = float(match.replace(",", ""))
                                    if 100 <= price <= 1000000:  # Reasonable price range
                                        logger.info(f"Successfully extracted price from Noon: {price}")

                                        # Apply discount for used items
                                        if self.text_processor.is_used_item(description):
                                            price *= EstimationConfig.USED_DISCOUNT_FACTOR

                                        return round(price, 2)
                                except ValueError:
                                    continue
                except Exception as e:
                    logger.debug(f"Error with selector '{selector}': {str(e)}")
                    continue

            logger.warning("No valid price found with any selector on Noon")

            # Fallback: try to find any text containing EGP or numbers
            try:
                logger.debug("Trying fallback: searching for any text containing prices")
                page_source = driver.page_source

                # Look for EGP patterns in page source
                egp_patterns = [
                    r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*EGP',
                    r'EGP\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
                    r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*ج\.م',
                    r'ج\.م\s*(\d+(?:,\d{3})*(?:\.\d{2})?)'
                ]

                for pattern in egp_patterns:
                    matches = re.findall(pattern, page_source)
                    if matches:
                        logger.debug(f"Found price patterns in page source: {matches[:5]}")  # Show first 5
                        for match in matches:
                            try:
                                price = float(match.replace(",", ""))
                                if 100 <= price <= 1000000:  # Reasonable price range
                                    logger.info(f"Extracted price from page source: {price}")

                                    # Apply discount for used items
                                    if self.text_processor.is_used_item(description):
                                        price *= EstimationConfig.USED_DISCOUNT_FACTOR

                                    return round(price, 2)
                            except ValueError:
                                continue

                logger.debug("No valid prices found in page source either")

            except Exception as e:
                logger.debug(f"Fallback price extraction failed: {str(e)}")

            return "No price found on Noon"

        except Exception as e:
            logger.error(f"Noon scraping error: {str(e)}")
            raise PriceEstimationError(f"Noon estimation failed: {str(e)}")
        finally:
            if driver:
                driver.quit()
