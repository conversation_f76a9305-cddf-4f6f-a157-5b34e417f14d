"""
Noon.com Egypt price estimator.
"""

import logging
import time
import re
from typing import Union

from selenium.webdriver.common.by import By

from .base import BasePriceEstimator
from ..utils.web_scraping import WebScrapingUtils
from ..utils.search_strategies import SearchStrategyGenerator
from ..utils.fallback_estimator import FallbackEstimator
from ..config.estimation_config import EstimationConfig

logger = logging.getLogger('price_estimation')


class NoonPriceEstimator(BasePriceEstimator):
    """Noon.com Egypt price estimator using Selenium with proper utilities."""

    def __init__(self):
        super().__init__()
        self.strategy_generator = SearchStrategyGenerator()
        self.fallback_estimator = FallbackEstimator()

    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Noon.com Egypt using Selenium."""
        logger.info(f"Starting Noon price estimation for: {name}")

        # Use simple search like Amazon - just the product name
        search_query = f"{name}".replace(" ", "+")
        url = f"https://www.noon.com/egypt-en/search?q={search_query}"

        logger.debug(f"Noon URL: {url}")

        driver = None
        try:
            driver = WebScrapingUtils.get_webdriver()
            driver.get(url)
            time.sleep(5)  # Wait longer for page to load

            # Try scrolling down to load more content
            try:
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
                time.sleep(2)
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
            except Exception:
                pass

            # Try to extract prices using text processor
            prices = self._extract_prices_from_page(driver, name, description)

            if prices:
                # Calculate average price from found listings
                average_price = sum(prices) / len(prices)

                # Apply discount for used items
                if self.text_processor.is_used_item(description):
                    average_price *= EstimationConfig.USED_DISCOUNT_FACTOR

                logger.info(f"Found {len(prices)} prices on Noon")
                return round(average_price, 2)

            logger.debug(f"No prices found on Noon")

        except Exception as e:
            logger.warning(f"Noon scraping failed: {str(e)}")
        finally:
            if driver:
                driver.quit()

        # If all strategies failed, try fallback
        logger.warning("All Noon search strategies failed, trying fallback approach")
        try:
            return self.fallback_estimator.estimate_dubizzle_fallback(name, description)
        except Exception as e:
            return f"Noon estimation failed: {str(e)}"

    def _extract_prices_from_page(self, driver, name: str, description: str) -> list:
        """Extract prices from Noon page using text processor."""
        prices = []

        try:
            # Debug: Check what's on the page
            page_title = driver.title
            logger.debug(f"Noon page title: {page_title}")

            # Debug: Save page source to see what's actually there
            page_source = driver.page_source
            logger.debug(f"Page source length: {len(page_source)}")

            # Look for any text containing "EGP" or numbers that look like prices
            if "EGP" in page_source:
                logger.debug("Found 'EGP' in page source")
                # Extract a sample of text around EGP
                import re
                egp_matches = re.findall(r'.{0,20}EGP.{0,20}', page_source)
                logger.debug(f"EGP context samples: {egp_matches[:5]}")
            else:
                logger.debug("No 'EGP' found in page source")

            # Try specific Noon price selectors first
            price_selectors = [
                ".currency",
                "[data-qa='product-price']",
                ".price",
                ".product-price",
                "[class*='price']",
                # Add more Noon-specific selectors
                "[data-testid*='price']",
                ".priceNow",
                ".sc-",  # Styled components
                "span[class*='currency']",
                "div[class*='currency']"
            ]

            for selector in price_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    logger.debug(f"Selector '{selector}' found {len(elements)} elements")

                    for element in elements[:20]:  # Limit to first 20 elements
                        text = element.text.strip()
                        if text:
                            price = self.text_processor.extract_price_from_text(text)
                            if price:
                                logger.debug(f"Found price: {price} from text: {text[:50]}...")
                                prices.append(price)
                except Exception:
                    continue

            # If no prices found with specific selectors, try broader search
            if not prices:
                logger.debug("No prices found with specific selectors, trying broader search")
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, "span, div")
                    for element in elements[:200]:  # Check more elements
                        text = element.text.strip()
                        if text and ("EGP" in text or "ج.م" in text or re.search(r'\d{3,}', text)):
                            price = self.text_processor.extract_price_from_text(text)
                            if price:
                                logger.debug(f"Found price: {price} from text: {text[:50]}...")
                                prices.append(price)
                                if len(prices) >= 10:  # Stop after finding 10 prices
                                    break
                except Exception:
                    pass

            # If still no prices, try direct regex on page source
            if not prices:
                logger.debug("No prices found with element search, trying regex on page source")
                try:
                    # Look for price patterns in the page source
                    price_patterns = [
                        r'(\d{1,2},?\d{3})\s*EGP',  # 7,200 EGP or 7200 EGP
                        r'EGP\s*(\d{1,2},?\d{3})',  # EGP 7,200 or EGP 7200
                        r'(\d{1,2},?\d{3})\s*ج\.م',  # 7,200 ج.م
                        r'ج\.م\s*(\d{1,2},?\d{3})',  # ج.م 7,200
                        r'"price"[^}]*?(\d{1,2},?\d{3})',  # JSON price field
                        r'"amount"[^}]*?(\d{1,2},?\d{3})',  # JSON amount field
                    ]

                    for pattern in price_patterns:
                        matches = re.findall(pattern, page_source)
                        logger.debug(f"Pattern '{pattern}' found {len(matches)} matches")
                        for match in matches:
                            try:
                                # Clean the match and convert to float
                                clean_price = match.replace(",", "").strip()
                                price = float(clean_price)
                                if 1000 <= price <= 100000:  # Reasonable price range for TVs
                                    logger.debug(f"Found price from regex: {price}")
                                    prices.append(price)
                                    if len(prices) >= 10:
                                        break
                            except ValueError:
                                continue
                        if prices:
                            break
                except Exception as e:
                    logger.debug(f"Regex price extraction failed: {str(e)}")
                    pass

            logger.debug(f"Extracted {len(prices)} valid prices from Noon")

        except Exception as e:
            logger.error(f"Error extracting prices from Noon page: {str(e)}")

        return prices[:10]  # Return max 10 prices
