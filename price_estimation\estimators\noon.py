"""
Noon.com Egypt price estimator.
"""

import logging
import time
from typing import Union

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from .base import BasePriceEstimator, PriceEstimationError
from ..utils.web_scraping import WebScrapingUtils
from ..config.estimation_config import EstimationConfig

logger = logging.getLogger('price_estimation')


class NoonPriceEstimator(BasePriceEstimator):
    """Noon.com Egypt price estimator using Selenium."""

    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Noon.com Egypt using Selenium."""
        
        # Clean and prepare search query
        search_query = self._prepare_search_query(name, description)
        url = f"https://www.noon.com/egypt-en/search?q={search_query}"
        
        logger.debug(f"Noon URL: {url}")
        
        driver = None
        try:
            driver = WebScrapingUtils.get_webdriver()
            driver.get(url)
            
            # Wait for page to load and handle any popups
            self._handle_page_load(driver)
            
            # Find product listings
            prices = self._extract_prices_from_listings(driver, name, description)
            
            if prices:
                # Calculate average price from found listings
                average_price = sum(prices) / len(prices)
                
                # Apply discount for used items
                if self.text_processor.is_used_item(description):
                    average_price *= EstimationConfig.USED_DISCOUNT_FACTOR
                
                logger.info(f"Found {len(prices)} prices on Noon")
                return round(average_price, 2)
            
            return "No price found on Noon"
            
        except Exception as e:
            logger.error(f"Noon scraping error: {str(e)}")
            raise PriceEstimationError(f"Noon estimation failed: {str(e)}")
        finally:
            if driver:
                driver.quit()

    def _prepare_search_query(self, name: str, description: str) -> str:
        """Prepare search query for Noon.com."""
        # Combine name and description, clean it
        full_text = f"{name} {description}"
        cleaned_text = self.text_processor.clean_search_text(full_text)
        
        # Extract key terms for better search results
        brand = self.text_processor.extract_brand(name, description, EstimationConfig.BRANDS)
        
        # Build search query with most relevant terms
        search_terms = []
        
        if brand:
            search_terms.append(brand)
        
        # Add first few words from name
        name_words = cleaned_text.split()[:3]
        search_terms.extend(name_words)
        
        # Join and clean
        search_query = " ".join(search_terms)
        return search_query.replace(" ", "%20")

    def _handle_page_load(self, driver):
        """Handle page loading and any popups/overlays."""
        try:
            # Wait for search results to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-qa='product-name'], .productContainer, .sc-fKVqWL"))
            )
            
            # Handle cookie consent popup if present
            try:
                cookie_button = WebDriverWait(driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-qa='accept-cookies'], .cookie-accept, #accept-cookies"))
                )
                cookie_button.click()
                time.sleep(1)
            except TimeoutException:
                pass  # No cookie popup found
            
            # Handle location popup if present
            try:
                location_close = WebDriverWait(driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, ".modal-close, [data-qa='close-modal'], .close-button"))
                )
                location_close.click()
                time.sleep(1)
            except TimeoutException:
                pass  # No location popup found
                
        except TimeoutException:
            logger.warning("Noon page took too long to load or no products found")

    def _extract_prices_from_listings(self, driver, name: str, description: str) -> list:
        """Extract prices from Noon product listings."""
        prices = []
        
        try:
            # Multiple selectors for product containers (Noon changes their structure frequently)
            product_selectors = [
                "[data-qa='product-name']",
                ".productContainer",
                ".sc-fKVqWL",
                ".product-card",
                "[data-testid='product-box']"
            ]
            
            products = []
            for selector in product_selectors:
                try:
                    products = driver.find_elements(By.CSS_SELECTOR, selector)
                    if products:
                        logger.debug(f"Found {len(products)} products using selector: {selector}")
                        break
                except NoSuchElementException:
                    continue
            
            if not products:
                logger.warning("No product containers found on Noon")
                return prices
            
            # Process first 10 products to avoid too many requests
            for product in products[:10]:
                try:
                    price = self._extract_price_from_product(product, name, description)
                    if price:
                        prices.append(price)
                except Exception as e:
                    logger.debug(f"Error extracting price from product: {str(e)}")
                    continue
            
            logger.debug(f"Extracted {len(prices)} valid prices from Noon")
            
        except Exception as e:
            logger.error(f"Error extracting prices from Noon listings: {str(e)}")
        
        return prices

    def _extract_price_from_product(self, product_element, name: str, description: str) -> Union[float, None]:
        """Extract price from a single Noon product element."""
        try:
            # Try to find product title
            title_selectors = [
                "[data-qa='product-name']",
                ".productName",
                ".sc-bdfBwQ",
                "h3",
                ".product-title"
            ]
            
            title = None
            for selector in title_selectors:
                try:
                    title_element = product_element.find_element(By.CSS_SELECTOR, selector)
                    title = title_element.text.strip()
                    if title:
                        break
                except NoSuchElementException:
                    continue
            
            if not title:
                return None
            
            # Check if title matches our search criteria
            search_text = f"{name} {description}".lower()
            similarity = self.text_processor.calculate_similarity(title.lower(), search_text)
            
            if similarity < EstimationConfig.SIMILARITY_THRESHOLD:
                return None
            
            # Try to find price
            price_selectors = [
                "[data-qa='product-price']",
                ".currency",
                ".sc-dlfnbm",
                ".price",
                ".product-price",
                ".priceNow"
            ]
            
            for selector in price_selectors:
                try:
                    price_element = product_element.find_element(By.CSS_SELECTOR, selector)
                    price_text = price_element.text.strip()
                    
                    # Extract price using text processor
                    price = self.text_processor.extract_price_from_text(price_text)
                    if price:
                        logger.debug(f"Found matching product on Noon: {title[:50]}... - Price: {price}")
                        return price
                        
                except NoSuchElementException:
                    continue
            
            logger.debug(f"Could not find price for product: {title[:50]}...")
            return None
            
        except Exception as e:
            logger.debug(f"Error processing Noon product: {str(e)}")
            return None
