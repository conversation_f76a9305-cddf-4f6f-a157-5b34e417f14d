"""
Noon.com Egypt price estimator.
"""

import logging
import time
import re
from typing import Union

from selenium.webdriver.common.by import By

from .base import BasePriceEstimator
from ..utils.web_scraping import WebScrapingUtils
from ..utils.search_strategies import SearchStrategyGenerator
from ..utils.fallback_estimator import FallbackEstimator
from ..config.estimation_config import EstimationConfig

logger = logging.getLogger('price_estimation')


class NoonPriceEstimator(BasePriceEstimator):
    """Noon.com Egypt price estimator using Selenium with proper utilities."""

    def __init__(self):
        super().__init__()
        self.strategy_generator = SearchStrategyGenerator()
        self.fallback_estimator = FallbackEstimator()

    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Noon.com Egypt using Selenium."""
        logger.info(f"Starting Noon price estimation for: {name}")

        # Generate search strategies using the utility
        search_strategies = self.strategy_generator.generate_dubizzle_strategies(name, description)

        # Try each strategy
        for search_terms in search_strategies:
            logger.debug(f"Trying Noon search strategy: {search_terms}")

            # Create URL with proper encoding
            search_query = search_terms.replace(" ", "+")
            url = f"https://www.noon.com/egypt-en/search?q={search_query}"

            logger.debug(f"Noon URL: {url}")

            driver = None
            try:
                driver = WebScrapingUtils.get_webdriver()
                driver.get(url)
                time.sleep(3)  # Wait for page to load

                # Try to extract prices using text processor
                prices = self._extract_prices_from_page(driver, name, description)

                if prices:
                    # Calculate average price from found listings
                    average_price = sum(prices) / len(prices)

                    # Apply discount for used items
                    if self.text_processor.is_used_item(description):
                        average_price *= EstimationConfig.USED_DISCOUNT_FACTOR

                    logger.info(f"Found {len(prices)} prices on Noon using strategy: {search_terms}")
                    return round(average_price, 2)

                logger.debug(f"No prices found with strategy: {search_terms}")

            except Exception as e:
                logger.warning(f"Noon scraping failed for strategy {search_terms}: {str(e)}")
                continue
            finally:
                if driver:
                    driver.quit()

        # If all strategies failed, try fallback
        logger.warning("All Noon search strategies failed, trying fallback approach")
        try:
            return self.fallback_estimator.estimate_dubizzle_fallback(name, description)
        except Exception as e:
            return f"Noon estimation failed: {str(e)}"

    def _extract_prices_from_page(self, driver, name: str, description: str) -> list:
        """Extract prices from Noon page using text processor."""
        prices = []

        try:
            # Debug: Check what's on the page
            page_title = driver.title
            logger.debug(f"Noon page title: {page_title}")

            # Use text processor to extract prices from page source
            page_source = driver.page_source

            # Look for price patterns in the page source
            price_text_elements = []

            # Try to find elements that might contain prices
            price_selectors = [
                "span", "div", "p"  # Broad selectors
            ]

            for selector in price_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements[:100]:  # Limit to first 100 elements
                        text = element.text.strip()
                        if text and ("EGP" in text or "ج.م" in text or re.search(r'\d{3,}', text)):
                            price_text_elements.append(text)
                except Exception:
                    continue

            # Extract prices using text processor
            for text in price_text_elements:
                price = self.text_processor.extract_price_from_text(text)
                if price:
                    logger.debug(f"Found price: {price} from text: {text[:50]}...")
                    prices.append(price)

            logger.debug(f"Extracted {len(prices)} valid prices from Noon")

        except Exception as e:
            logger.error(f"Error extracting prices from Noon page: {str(e)}")

        return prices[:10]  # Return max 10 prices
