"""
Price estimation services for Egyptian electronics marketplace.

Provides intelligent price estimation by scraping Amazon Egypt and Dubizzle Egypt,
with enhanced Arabic support and fallback strategies.
"""

import hashlib
import logging
import time
from datetime import timed<PERSON><PERSON>
from typing import Dict, Optional, Union
from decimal import Decimal

import requests
from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By

from django.utils import timezone
from .models import PriceEstimationCache
from .utils.web_scraping import WebScrapingUtils
from .utils.text_processing import TextProcessor
from .config.estimation_config import EstimationConfig

logger = logging.getLogger('price_estimation')


class PriceEstimationError(Exception):
    """Custom exception for price estimation errors."""
    pass


class BasePriceEstimator:
    """
    Base class for price estimators following DRY principles.

    This abstract base class defines the common interface and
    shared functionality for all price estimation sources.
    """

    def __init__(self):
        self.session = WebScrapingUtils.create_session()
        self.text_processor = TextProcessor()

    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Abstract method to be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement estimate_price method")


class AmazonPriceEstimator(BasePriceEstimator):
    """Amazon Egypt price estimator using Selenium."""

    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Amazon Egypt using Selenium."""
        import re

        search_query = f"{name} {description}".replace(" ", "+")
        url = f"https://www.amazon.eg/s?k={search_query}"

        logger.debug(f"Amazon URL: {url}")

        driver = None
        try:
            driver = WebScrapingUtils.get_webdriver()
            driver.get(url)
            time.sleep(2)  # Wait for page to load

            # Find price elements
            price_elements = driver.find_elements(By.CLASS_NAME, "a-price-whole")

            for price_element in price_elements:
                price_text = price_element.text.replace(",", "").strip()
                if re.match(r'^\d+', price_text):
                    price = float(price_text)

                    # Apply discount for used items
                    if self.text_processor.is_used_item(description):
                        price *= EstimationConfig.USED_DISCOUNT_FACTOR

                    return round(price, 2)

            return "No price found on Amazon"

        except Exception as e:
            logger.error(f"Amazon scraping error: {str(e)}")
            raise PriceEstimationError(f"Amazon estimation failed: {str(e)}")
        finally:
            if driver:
                driver.quit()


class DubizzlePriceEstimator(BasePriceEstimator):
    """
    Dubizzle Egypt price estimator using requests.

    This estimator is more challenging than Amazon because:
    - User-generated content with inconsistent formats
    - URL length and character sensitivity
    - Heavy Arabic content mixing
    - More aggressive anti-bot measures
    """

    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Dubizzle Egypt using requests."""
        # Prepare multiple search strategies with increasing simplicity
        search_strategies = self._prepare_search_strategies(name, description)

        for search_terms in search_strategies:
            urls_to_try = WebScrapingUtils.generate_dubizzle_urls(search_terms)

            for url in urls_to_try:
                try:
                    logger.debug(f"Trying Dubizzle URL: {url}")

                    response = self.session.get(
                        url,
                        headers=WebScrapingUtils.get_dubizzle_headers(),
                        timeout=15
                    )

                    if response.status_code == 200:
                        prices = self._extract_prices_from_page(response.text, name, description)
                        if prices:
                            average_price = sum(prices) / len(prices)
                            logger.info(f"Found {len(prices)} matching prices on Dubizzle")
                            return round(average_price, 2)
                    else:
                        logger.warning(f"Dubizzle returned status {response.status_code} for URL: {url}")
                        continue

                except requests.exceptions.RequestException as e:
                    logger.warning(f"Dubizzle request failed for URL {url}: {str(e)}")
                    continue
                except Exception as e:
                    logger.warning(f"Dubizzle parsing failed for URL {url}: {str(e)}")
                    continue

        # If all URLs failed, try a simple fallback approach
        logger.warning("All Dubizzle URL strategies failed, trying fallback approach")
        return self._fallback_dubizzle_estimation(name, description)

    def _extract_prices_from_page(self, html_content: str, name: str, description: str) -> list:
        """Extract prices from Dubizzle page HTML."""
        soup = BeautifulSoup(html_content, 'html.parser')

        # Try multiple selectors for listings
        listings = (
            soup.find_all('article') or
            soup.find_all('div', class_='listing') or
            soup.find_all('div', class_='item') or
            soup.find_all('div', attrs={'data-testid': 'listing-card'})
        )

        logger.debug(f"Found {len(listings)} potential listings")

        prices = []
        for listing in listings[:20]:  # Limit to first 20 listings
            price = self._extract_price_from_listing(listing, name, description)
            if price:
                prices.append(price)

        logger.debug(f"Extracted {len(prices)} valid prices")
        return prices
    
    def _prepare_search_strategies(self, name: str, description: str) -> list:
        """
        Prepare multiple search strategies with increasing simplicity.

        This approach tries different levels of detail to maximize success:
        1. Detailed search with key terms
        2. Brand + model only
        3. Brand + category only
        4. Just the main product name
        """
        strategies = []

        # Clean the inputs
        name_clean = self.text_processor.clean_search_text(name)
        description_clean = self.text_processor.clean_search_text(description)

        # Extract key information
        brand = self.text_processor.extract_brand(name_clean, description_clean, EstimationConfig.BRANDS)
        category = self.text_processor.extract_category(name_clean, description_clean, EstimationConfig.CATEGORIES)
        key_specs = self.text_processor.extract_key_specs(description_clean)

        # Determine if item is new or used for Arabic keyword enhancement
        is_used = self.text_processor.is_used_item(f"{name} {description}")
        condition_keyword = "" if is_used else " جديد"  # Add "new" in Arabic for new items

        # Strategy 1: Brand + key specs (most specific)
        if brand and key_specs:
            strategy1 = f"{brand} {' '.join(key_specs[:2])}{condition_keyword}"
            if len(strategy1) <= EstimationConfig.MAX_SEARCH_LENGTH:
                strategies.append(strategy1.lower())

        # Strategy 2: Brand + category
        if brand and category:
            strategy2 = f"{brand} {category}{condition_keyword}"
            if len(strategy2) <= EstimationConfig.MAX_SEARCH_LENGTH:
                strategies.append(strategy2.lower())

        # Strategy 3: Just brand + first word of name
        if brand:
            first_word = name_clean.split()[0] if name_clean.split() else ""
            if first_word and first_word.lower() != brand.lower():
                strategy3 = f"{brand} {first_word}{condition_keyword}"
                if len(strategy3) <= EstimationConfig.MAX_SEARCH_LENGTH:
                    strategies.append(strategy3.lower())

        # Strategy 4: Just the brand
        if brand:
            strategy4 = f"{brand}{condition_keyword}"
            if len(strategy4) <= EstimationConfig.MAX_SEARCH_LENGTH:
                strategies.append(strategy4.lower())

        # Strategy 5: First few words of name (fallback)
        name_words = name_clean.split()[:3]  # Max 3 words
        if name_words:
            fallback = f"{' '.join(name_words)}{condition_keyword}"
            if len(fallback) <= EstimationConfig.MAX_SEARCH_LENGTH:
                strategies.append(fallback.lower())

        # Remove duplicates while preserving order
        unique_strategies = []
        for strategy in strategies:
            if strategy not in unique_strategies:
                unique_strategies.append(strategy)

        return unique_strategies[:4]  # Max 4 strategies to avoid too many requests

    def _extract_price_from_listing(self, listing, name: str, description: str) -> Optional[float]:
        """Extract price from a Dubizzle listing if it matches the product."""
        import re

        # Try multiple selectors for title
        title_element = (
            listing.find('h2') or
            listing.find('h3') or
            listing.find('a', class_='title') or
            listing.find('div', class_='title') or
            listing.find('[data-testid="listing-title"]')
        )

        # Try multiple selectors for price
        price_element = (
            listing.find('span', class_="_1f2a2b47") or
            listing.find('span', class_='price') or
            listing.find('div', class_='price') or
            listing.find('[data-testid="listing-price"]') or
            listing.find('span', string=re.compile(r'\d+.*EGP|جنيه')) or
            listing.find('div', string=re.compile(r'\d+.*EGP|جنيه'))
        )

        # If we can't find both title and price, skip this listing
        if not title_element or not price_element:
            return None

        title = title_element.get_text().strip()
        logger.debug(f"Found listing title: {title[:50]}...")

        # Calculate similarity with a more lenient approach
        search_text = f"{name} {description}".lower()
        similarity = self.text_processor.calculate_similarity(title.lower(), search_text)

        logger.debug(f"Similarity score: {round(similarity, 2)} (threshold: {EstimationConfig.SIMILARITY_THRESHOLD})")

        if similarity >= EstimationConfig.SIMILARITY_THRESHOLD:
            logger.debug(f"Matched listing: {title} (Similarity: {round(similarity, 2)})")

            price_text = price_element.get_text().replace(",", "").strip()
            logger.debug(f"Price text: {price_text}")

            price = self.text_processor.extract_price_from_text(price_text)
            if price:
                logger.debug(f"Extracted price: {price}")
                return price

        return None
    
    def _fallback_dubizzle_estimation(self, name: str, description: str) -> str:
        """
        Fallback method when all Dubizzle scraping attempts fail.

        Since Dubizzle has changed their URL structure and search endpoints,
        we provide a graceful fallback that still adds value to users.
        """
        logger.info("Using fallback Dubizzle estimation method")

        # Try to provide a reasonable estimate based on product category and condition
        try:
            # Extract brand and category for basic estimation
            brand = self.text_processor.extract_brand(name, description, EstimationConfig.BRANDS)
            category = self.text_processor.extract_category(name, description, EstimationConfig.CATEGORIES)

            # Get price range
            category_prices = EstimationConfig.PRICE_RANGES.get(category, EstimationConfig.PRICE_RANGES['default'])
            brand_range = category_prices.get(brand, category_prices['default'])

            # Calculate middle price (used market typically 60-70% of new)
            min_price, max_price = brand_range
            if self.text_processor.is_used_item(description) or self.text_processor.is_used_item(name):
                estimated_price = (min_price + max_price) * 0.35  # 35% of average for used
            else:
                estimated_price = (min_price + max_price) * 0.5   # 50% of average for new

            logger.info(f"Fallback estimation: {estimated_price} EGP for {brand} {category}")
            return round(estimated_price, 2)

        except Exception as e:
            logger.error(f"Fallback estimation failed: {str(e)}")
            return "Dubizzle temporarily unavailable - using Amazon price only"


class PriceEstimatorService:
    """
    Main service class for price estimation following Django best practices.

    This service:
    - Uses dependency injection for estimators
    - Implements caching to avoid repeated requests
    - Follows DRY principles
    - Provides comprehensive error handling
    """

    def __init__(self):
        self.estimators = {
            'amazon': AmazonPriceEstimator(),
            'dubizzle': DubizzlePriceEstimator(),
        }
    
    def estimate_price(self, name: str, description: str, use_cache: bool = True) -> Dict[str, Union[float, str, None]]:
        """
        Main method to estimate price from multiple sources.
        
        Args:
            name: Product name
            description: Product description
            use_cache: Whether to use cached results
            
        Returns:
            Dictionary containing price estimates and metadata
        """
        logger.info(f"Starting price estimation for: {name}")
        
        # Check cache first if enabled
        if use_cache:
            cached_result = self._get_cached_result(name, description)
            if cached_result:
                logger.info(f"Returning cached result for: {name}")
                return cached_result
        
        result = {
            'amazon_price': None,
            'dubizzle_price': None,
            'estimated_price': None,
            'sources_used': [],
            'estimation_date': timezone.now(),
            'errors': []
        }
        
        # Get prices from all estimators
        for source_name, estimator in self.estimators.items():
            try:
                price = estimator.estimate_price(name, description)
                if isinstance(price, (int, float)):
                    result[f'{source_name}_price'] = float(price)
                    result['sources_used'].append(source_name)
                else:
                    result['errors'].append(f"{source_name}: {price}")
            except Exception as e:
                logger.error(f"{source_name} price estimation failed: {str(e)}")
                result['errors'].append(f"{source_name}: {str(e)}")
        
        # Calculate final estimate
        result['estimated_price'] = self._calculate_final_estimate(
            result['amazon_price'], 
            result['dubizzle_price']
        )
        
        # Cache the result if caching is enabled
        if use_cache:
            self._cache_result(name, description, result)

        logger.info(f"Price estimation completed for: {name}. Estimated price: {result['estimated_price']}")
        return result

    def _calculate_final_estimate(self, amazon_price: Optional[float], dubizzle_price: Optional[float]) -> Optional[float]:
        """Calculate final price estimate from available sources."""
        if amazon_price and dubizzle_price:
            return round((amazon_price + dubizzle_price) / 2, 2)
        elif amazon_price:
            return amazon_price
        elif dubizzle_price:
            return dubizzle_price
        return None

    def _generate_cache_key(self, name: str, description: str) -> str:
        """Generate cache key for price estimation."""
        content = f"{name}_{description}".lower()
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_result(self, name: str, description: str) -> Optional[Dict]:
        """Get cached price estimation result."""
        cache_key = self._generate_cache_key(name, description)

        try:
            cache_entry = PriceEstimationCache.objects.get(
                cache_key=cache_key,
                expires_at__gt=timezone.now()
            )

            # Increment hit count
            cache_entry.increment_hit_count()

            return {
                'amazon_price': float(cache_entry.amazon_price) if cache_entry.amazon_price else None,
                'dubizzle_price': float(cache_entry.dubizzle_price) if cache_entry.dubizzle_price else None,
                'estimated_price': float(cache_entry.estimated_price) if cache_entry.estimated_price else None,
                'sources_used': cache_entry.sources_used,
                'estimation_date': cache_entry.created_at,
                'errors': cache_entry.estimation_errors,
                'cached': True
            }
        except PriceEstimationCache.DoesNotExist:
            return None

    def _cache_result(self, name: str, description: str, result: Dict) -> None:
        """Cache price estimation result."""
        cache_key = self._generate_cache_key(name, description)
        expires_at = timezone.now() + timedelta(seconds=EstimationConfig.CACHE_TIMEOUT)

        try:
            # Update existing cache entry or create new one
            PriceEstimationCache.objects.update_or_create(
                cache_key=cache_key,
                defaults={
                    'estimated_price': self._safe_decimal_conversion(result['estimated_price']),
                    'amazon_price': self._safe_decimal_conversion(result['amazon_price']),
                    'dubizzle_price': self._safe_decimal_conversion(result['dubizzle_price']),
                    'sources_used': result['sources_used'],
                    'estimation_errors': result['errors'],
                    'expires_at': expires_at,
                }
            )

            logger.debug(f"Cached result for: {name}")

        except Exception as e:
            logger.error(f"Failed to cache result for {name}: {str(e)}")
            # Don't raise exception for caching failures

    def _safe_decimal_conversion(self, value):
        """Safely convert a value to Decimal, handling various edge cases."""
        if value is None:
            return None
        try:
            # Handle string values that might not be numeric
            if isinstance(value, str):
                # Remove any non-numeric characters except decimal point and minus
                cleaned_value = ''.join(c for c in value if c.isdigit() or c in '.-')
                if not cleaned_value or cleaned_value in ['.', '-', '.-']:
                    return None
                return Decimal(cleaned_value)
            else:
                return Decimal(str(value))
        except (ValueError, TypeError, Exception):
            return None
