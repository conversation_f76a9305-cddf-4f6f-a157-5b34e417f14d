"""
Price estimation services for Egyptian electronics marketplace.

Provides intelligent price estimation by scraping Amazon Egypt and Dubizzle Egypt,
with enhanced Arabic support and fallback strategies.
"""

import hashlib
import logging
import re
import time
from datetime import timed<PERSON><PERSON>
from typing import Dict, Optional, Union
from decimal import Decimal
import difflib

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager

from django.utils import timezone
from .models import PriceEstimationCache

logger = logging.getLogger('price_estimation')


class PriceEstimationError(Exception):
    """Custom exception for price estimation errors."""
    pass


class BasePriceEstimator:
    """
    Base class for price estimators following DRY principles.
    
    This abstract base class defines the common interface and
    shared functionality for all price estimation sources.
    """
    
    def __init__(self):
        self.session = self._create_session()
    
    def _create_session(self):
        """Create and configure requests session."""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
        })
        return session
    
    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Abstract method to be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement estimate_price method")
    
    def _is_used_item(self, text: str) -> bool:
        """
        Enhanced condition detection with Arabic support.

        Detects various ways to express 'used' condition in both English and Arabic.
        """
        text_lower = text.lower()

        # English used indicators
        used_english = ["used", "second hand", "pre-owned", "refurbished", "open box"]

        # Arabic used indicators
        used_arabic = ["مستعمل", "مستخدم", "مستعمله", "ثاني هاند", "مفتوح"]

        # Check English terms
        for term in used_english:
            if term in text_lower:
                return True

        # Check Arabic terms (case-sensitive for Arabic)
        for term in used_arabic:
            if term in text:
                return True

        return False
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity score between two texts."""
        return difflib.SequenceMatcher(None, text1.lower(), text2.lower()).ratio()


class AmazonPriceEstimator(BasePriceEstimator):
    """Amazon Egypt price estimator using Selenium."""
    
    USED_DISCOUNT_FACTOR = 0.8
    
    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Amazon Egypt using Selenium."""
        search_query = f"{name} {description}".replace(" ", "+")
        url = f"https://www.amazon.eg/s?k={search_query}"
        
        logger.debug(f"Amazon URL: {url}")
        
        driver = None
        try:
            driver = self._get_webdriver()
            driver.get(url)
            time.sleep(2)  # Wait for page to load
            
            # Find price elements
            price_elements = driver.find_elements(By.CLASS_NAME, "a-price-whole")
            
            for price_element in price_elements:
                price_text = price_element.text.replace(",", "").strip()
                if re.match(r'^\d+', price_text):
                    price = float(price_text)
                    
                    # Apply discount for used items
                    if self._is_used_item(description):
                        price *= self.USED_DISCOUNT_FACTOR
                    
                    return round(price, 2)
            
            return "No price found on Amazon"
            
        except Exception as e:
            logger.error(f"Amazon scraping error: {str(e)}")
            raise PriceEstimationError(f"Amazon estimation failed: {str(e)}")
        finally:
            if driver:
                driver.quit()
    
    def _get_webdriver(self) -> webdriver.Chrome:
        """Get configured Chrome WebDriver."""
        options = Options()
        options.add_argument("--headless=new")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            logger.error(f"WebDriver initialization failed: {str(e)}")
            raise PriceEstimationError(f"WebDriver setup failed: {str(e)}")


class DubizzlePriceEstimator(BasePriceEstimator):
    """
    Dubizzle Egypt price estimator using requests.

    This estimator is more challenging than Amazon because:
    - User-generated content with inconsistent formats
    - URL length and character sensitivity
    - Heavy Arabic content mixing
    - More aggressive anti-bot measures
    """

    SIMILARITY_THRESHOLD = 0.58
    MAX_SEARCH_LENGTH = 50  # Conservative limit for Dubizzle URLs
    
    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Dubizzle Egypt using requests."""
        import urllib.parse

        # Prepare multiple search strategies with increasing simplicity
        search_strategies = self._prepare_search_strategies(name, description)

        urls_to_try = []
        for search_terms in search_strategies:
            # Add multiple URL formats for each search strategy
            # Try different Dubizzle URL patterns as they may have changed
            urls_to_try.extend([
                f"https://www.dubizzle.com.eg/ads/q-{search_terms.replace(' ', '-')}/",
                f"https://www.dubizzle.com.eg/search?q={urllib.parse.quote(search_terms)}",
                f"https://www.dubizzle.com.eg/en/search?q={urllib.parse.quote(search_terms)}",
                f"https://www.dubizzle.com.eg/classified/{search_terms.replace(' ', '-')}",
            ])

        for url in urls_to_try:
            try:
                logger.debug(f"Trying Dubizzle URL: {url}")

                # Add headers to appear more like a real browser
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Referer': 'https://www.dubizzle.com.eg/',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'same-origin',
                }

                response = self.session.get(url, headers=headers, timeout=15)

                # Debug: Log response details
                logger.debug(f"Dubizzle response status: {response.status_code}")
                logger.debug(f"Response headers: {dict(response.headers)}")

                # Check if we got a successful response
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # Debug: Check page content
                    page_title = soup.find('title')
                    logger.debug(f"Page title: {page_title.text if page_title else 'No title'}")

                    # Try multiple selectors for listings
                    listings = (
                        soup.find_all('article') or
                        soup.find_all('div', class_='listing') or
                        soup.find_all('div', class_='item') or
                        soup.find_all('div', attrs={'data-testid': 'listing-card'})
                    )

                    logger.debug(f"Found {len(listings)} potential listings")

                    if listings:
                        prices = []
                        for listing in listings[:20]:  # Limit to first 20 listings
                            price = self._extract_price(listing, name, description)
                            if price:
                                prices.append(price)

                        logger.debug(f"Extracted {len(prices)} valid prices")

                        if prices:
                            # Calculate average price
                            average_price = sum(prices) / len(prices)
                            logger.info(f"Found {len(prices)} matching prices on Dubizzle")
                            return round(average_price, 2)

                    # If no listings found with this URL, try the next one
                    logger.debug(f"No valid listings found for URL: {url}")
                    continue
                else:
                    logger.warning(f"Dubizzle returned status {response.status_code} for URL: {url}")
                    # Log response content for debugging
                    if response.status_code in [403, 404]:
                        logger.debug(f"Response content preview: {response.text[:500]}")
                    continue

            except requests.exceptions.RequestException as e:
                logger.warning(f"Dubizzle request failed for URL {url}: {str(e)}")
                continue
            except Exception as e:
                logger.warning(f"Dubizzle parsing failed for URL {url}: {str(e)}")
                continue

        # If all URLs failed, try a simple fallback approach
        logger.warning("All Dubizzle URL strategies failed, trying fallback approach")
        return self._fallback_dubizzle_estimation(name, description)
    
    def _prepare_search_strategies(self, name: str, description: str) -> list:
        """
        Prepare multiple search strategies with increasing simplicity.

        This approach tries different levels of detail to maximize success:
        1. Detailed search with key terms
        2. Brand + model only
        3. Brand + category only
        4. Just the main product name
        """
        strategies = []

        # Clean the inputs
        name_clean = self._clean_search_text(name)
        description_clean = self._clean_search_text(description)

        # Extract key information
        brand = self._extract_brand(name_clean, description_clean)
        category = self._extract_category(name_clean, description_clean)
        key_specs = self._extract_key_specs(description_clean)

        # Determine if item is new or used for Arabic keyword enhancement
        is_used = self._is_used_item(f"{name} {description}")
        condition_keyword = "" if is_used else " جديد"  # Add "new" in Arabic for new items

        # Strategy 1: Brand + key specs (most specific)
        if brand and key_specs:
            strategy1 = f"{brand} {' '.join(key_specs[:2])}{condition_keyword}"
            if len(strategy1) <= self.MAX_SEARCH_LENGTH:
                strategies.append(strategy1.lower())

        # Strategy 2: Brand + category
        if brand and category:
            strategy2 = f"{brand} {category}{condition_keyword}"
            if len(strategy2) <= self.MAX_SEARCH_LENGTH:
                strategies.append(strategy2.lower())

        # Strategy 3: Just brand + first word of name
        if brand:
            first_word = name_clean.split()[0] if name_clean.split() else ""
            if first_word and first_word.lower() != brand.lower():
                strategy3 = f"{brand} {first_word}{condition_keyword}"
                if len(strategy3) <= self.MAX_SEARCH_LENGTH:
                    strategies.append(strategy3.lower())

        # Strategy 4: Just the brand
        if brand:
            strategy4 = f"{brand}{condition_keyword}"
            if len(strategy4) <= self.MAX_SEARCH_LENGTH:
                strategies.append(strategy4.lower())

        # Strategy 5: First few words of name (fallback)
        name_words = name_clean.split()[:3]  # Max 3 words
        if name_words:
            fallback = f"{' '.join(name_words)}{condition_keyword}"
            if len(fallback) <= self.MAX_SEARCH_LENGTH:
                strategies.append(fallback.lower())

        # Remove duplicates while preserving order
        unique_strategies = []
        for strategy in strategies:
            if strategy not in unique_strategies:
                unique_strategies.append(strategy)

        return unique_strategies[:4]  # Max 4 strategies to avoid too many requests

    def _extract_brand(self, name: str, description: str) -> str:
        """Extract brand name from product name or description."""
        # Common electronics brands in Egyptian market
        brands = [
            'samsung', 'apple', 'dell', 'hp', 'lenovo', 'asus', 'acer', 'sony', 'lg',
            'huawei', 'xiaomi', 'oppo', 'vivo', 'nokia', 'intel', 'amd', 'nvidia',
            'microsoft', 'google', 'canon', 'nikon', 'bose', 'jbl', 'beats',
            'logitech', 'razer', 'corsair', 'steelseries', 'hyperx'
        ]

        text = f"{name} {description}".lower()
        for brand in brands:
            if brand in text:
                return brand
        return ""

    def _extract_category(self, name: str, description: str) -> str:
        """Extract product category from name or description."""
        categories = {
            'laptop': ['laptop', 'notebook', 'macbook'],
            'phone': ['phone', 'iphone', 'smartphone', 'mobile'],
            'tablet': ['tablet', 'ipad'],
            'monitor': ['monitor', 'screen', 'display', 'شاشة'],
            'keyboard': ['keyboard', 'كيبورد'],
            'mouse': ['mouse', 'ماوس'],
            'headphones': ['headphones', 'earphones', 'headset'],
            'speaker': ['speaker', 'speakers'],
            'camera': ['camera', 'كاميرا'],
            'tv': ['tv', 'television', 'smart tv']
        }

        text = f"{name} {description}".lower()
        for category, keywords in categories.items():
            for keyword in keywords:
                if keyword in text:
                    return category
        return ""

    def _extract_key_specs(self, text: str) -> list:
        """Extract key specifications like size, storage, etc."""
        import re

        specs = []
        text_lower = text.lower()

        # Extract sizes (inches, GB, TB, etc.)
        sizes = re.findall(r'\b\d+(?:inch|gb|tb|hz)\b', text_lower)
        specs.extend(sizes[:2])  # Max 2 size specs

        # Extract standalone numbers that might be important
        numbers = re.findall(r'\b\d{1,3}\b', text_lower)
        specs.extend(numbers[:1])  # Max 1 standalone number

        return specs[:3]  # Max 3 specs total

    def _clean_search_text(self, text: str) -> str:
        """Clean text for URL-safe search terms."""
        import re

        # Remove newlines and extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())

        # Remove special characters that cause URL issues
        text = re.sub(r'[^\w\s\u0600-\u06FF-]', ' ', text)  # Keep Arabic, alphanumeric, spaces, hyphens

        # Remove model numbers and technical specs that are too specific
        text = re.sub(r'\b[A-Z0-9]{5,}\b', '', text)  # Remove long alphanumeric codes
        text = re.sub(r'\[[^\]]*\]', '', text)  # Remove content in brackets
        text = re.sub(r'\([^)]*\)', '', text)  # Remove content in parentheses

        # Clean up multiple spaces
        text = re.sub(r'\s+', ' ', text).strip()

        return text
    
    def _extract_price(self, listing, name: str, description: str) -> Optional[float]:
        """Extract price from a Dubizzle listing if it matches the product."""
        import re

        # Try multiple selectors for title
        title_element = (
            listing.find('h2') or
            listing.find('h3') or
            listing.find('a', class_='title') or
            listing.find('div', class_='title') or
            listing.find('[data-testid="listing-title"]')
        )

        # Try multiple selectors for price
        price_element = (
            listing.find('span', class_="_1f2a2b47") or
            listing.find('span', class_='price') or
            listing.find('div', class_='price') or
            listing.find('[data-testid="listing-price"]') or
            listing.find('span', string=re.compile(r'\d+.*EGP|جنيه')) or
            listing.find('div', string=re.compile(r'\d+.*EGP|جنيه'))
        )

        # If we can't find both title and price, skip this listing
        if not title_element:
            logger.debug("No title element found in listing")
            return None

        if not price_element:
            logger.debug("No price element found in listing")
            return None

        title = title_element.get_text().strip()
        logger.debug(f"Found listing title: {title[:50]}...")

        # Calculate similarity with a more lenient approach
        search_text = f"{name} {description}".lower()
        similarity = self._calculate_similarity(title.lower(), search_text)

        logger.debug(f"Similarity score: {round(similarity, 2)} (threshold: {self.SIMILARITY_THRESHOLD})")

        if similarity >= self.SIMILARITY_THRESHOLD:
            logger.debug(f"Matched listing: {title} (Similarity: {round(similarity, 2)})")

            price_text = price_element.get_text().replace(",", "").strip()
            logger.debug(f"Price text: {price_text}")

            # Try multiple regex patterns for price extraction
            price_patterns = [
                r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:EGP|جنيه|LE|L\.E\.)',  # With currency
                r'(\d+(?:,\d{3})*(?:\.\d{2})?)',  # Just numbers
                r'(\d+(?:\.\d{3})*(?:,\d{2})?)',  # European format
            ]

            for pattern in price_patterns:
                price_match = re.search(pattern, price_text)
                if price_match:
                    try:
                        price_str = price_match.group(1).replace(",", "")
                        price = float(price_str)
                        if 100 <= price <= 1000000:  # Reasonable price range
                            logger.debug(f"Extracted price: {price}")
                            return price
                    except ValueError:
                        continue

            logger.debug(f"Could not extract valid price from: {price_text}")

        return None

    def _fallback_dubizzle_estimation(self, name: str, description: str) -> str:
        """
        Fallback method when all Dubizzle scraping attempts fail.

        Since Dubizzle has changed their URL structure and search endpoints,
        we provide a graceful fallback that still adds value to users.
        """
        logger.info("Using fallback Dubizzle estimation method")

        # Try to provide a reasonable estimate based on product category and condition
        try:
            # Extract brand and category for basic estimation
            brand = self._extract_brand(name, description)
            category = self._extract_category(name, description)

            # Basic price ranges for Egyptian market (in EGP)
            price_ranges = {
                'phone': {'samsung': (3000, 25000), 'apple': (15000, 50000), 'xiaomi': (2000, 15000), 'default': (1500, 20000)},
                'laptop': {'dell': (15000, 60000), 'hp': (12000, 50000), 'lenovo': (10000, 45000), 'default': (8000, 40000)},
                'tablet': {'apple': (12000, 35000), 'samsung': (8000, 25000), 'default': (5000, 20000)},
                'monitor': {'samsung': (5000, 25000), 'lg': (4000, 20000), 'default': (3000, 15000)},
                'tv': {'samsung': (8000, 50000), 'lg': (7000, 40000), 'default': (5000, 30000)},
                'default': {'default': (1000, 10000)}
            }

            # Get price range
            category_prices = price_ranges.get(category, price_ranges['default'])
            brand_range = category_prices.get(brand, category_prices['default'])

            # Calculate middle price (used market typically 60-70% of new)
            min_price, max_price = brand_range
            if self._is_used_item(description) or self._is_used_item(name):
                estimated_price = (min_price + max_price) * 0.35  # 35% of average for used
            else:
                estimated_price = (min_price + max_price) * 0.5   # 50% of average for new

            logger.info(f"Fallback estimation: {estimated_price} EGP for {brand} {category}")
            return round(estimated_price, 2)

        except Exception as e:
            logger.error(f"Fallback estimation failed: {str(e)}")
            return "Dubizzle temporarily unavailable - using Amazon price only"


class PriceEstimatorService:
    """
    Main service class for price estimation following Django best practices.
    
    This service:
    - Uses dependency injection for estimators
    - Implements caching to avoid repeated requests
    - Follows DRY principles
    - Provides comprehensive error handling
    """
    
    # Cache timeout in seconds (1 hour)
    CACHE_TIMEOUT = 3600
    
    def __init__(self):
        self.estimators = {
            'amazon': AmazonPriceEstimator(),
            'dubizzle': DubizzlePriceEstimator(),
        }
    
    def estimate_price(self, name: str, description: str, use_cache: bool = True) -> Dict[str, Union[float, str, None]]:
        """
        Main method to estimate price from multiple sources.
        
        Args:
            name: Product name
            description: Product description
            use_cache: Whether to use cached results
            
        Returns:
            Dictionary containing price estimates and metadata
        """
        logger.info(f"Starting price estimation for: {name}")
        
        # Check cache first if enabled
        if use_cache:
            cached_result = self._get_cached_result(name, description)
            if cached_result:
                logger.info(f"Returning cached result for: {name}")
                return cached_result
        
        result = {
            'amazon_price': None,
            'dubizzle_price': None,
            'estimated_price': None,
            'sources_used': [],
            'estimation_date': timezone.now(),
            'errors': []
        }
        
        # Get prices from all estimators
        for source_name, estimator in self.estimators.items():
            try:
                price = estimator.estimate_price(name, description)
                if isinstance(price, (int, float)):
                    result[f'{source_name}_price'] = float(price)
                    result['sources_used'].append(source_name)
                else:
                    result['errors'].append(f"{source_name}: {price}")
            except Exception as e:
                logger.error(f"{source_name} price estimation failed: {str(e)}")
                result['errors'].append(f"{source_name}: {str(e)}")
        
        # Calculate final estimate
        result['estimated_price'] = self._calculate_final_estimate(
            result['amazon_price'], 
            result['dubizzle_price']
        )
        
        # Cache the result if caching is enabled
        if use_cache:
            self._cache_result(name, description, result)
        
        logger.info(f"Price estimation completed for: {name}. Estimated price: {result['estimated_price']}")
        return result
    
    def _calculate_final_estimate(self, amazon_price: Optional[float], dubizzle_price: Optional[float]) -> Optional[float]:
        """Calculate final price estimate from available sources."""
        if amazon_price and dubizzle_price:
            return round((amazon_price + dubizzle_price) / 2, 2)
        elif amazon_price:
            return amazon_price
        elif dubizzle_price:
            return dubizzle_price
        return None
    
    def _generate_cache_key(self, name: str, description: str) -> str:
        """Generate cache key for price estimation."""
        content = f"{name}_{description}".lower()
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cached_result(self, name: str, description: str) -> Optional[Dict]:
        """Get cached price estimation result."""
        cache_key = self._generate_cache_key(name, description)
        
        try:
            cache_entry = PriceEstimationCache.objects.get(
                cache_key=cache_key,
                expires_at__gt=timezone.now()
            )
            
            # Increment hit count
            cache_entry.increment_hit_count()
            
            return {
                'amazon_price': float(cache_entry.amazon_price) if cache_entry.amazon_price else None,
                'dubizzle_price': float(cache_entry.dubizzle_price) if cache_entry.dubizzle_price else None,
                'estimated_price': float(cache_entry.estimated_price) if cache_entry.estimated_price else None,
                'sources_used': cache_entry.sources_used,
                'estimation_date': cache_entry.created_at,
                'errors': cache_entry.estimation_errors,
                'cached': True
            }
        except PriceEstimationCache.DoesNotExist:
            return None
    
    def _cache_result(self, name: str, description: str, result: Dict) -> None:
        """Cache price estimation result."""
        cache_key = self._generate_cache_key(name, description)
        expires_at = timezone.now() + timedelta(seconds=self.CACHE_TIMEOUT)
        
        try:
            # Safely convert prices to Decimal with validation
            def safe_decimal_conversion(value):
                """Safely convert a value to Decimal, handling various edge cases."""
                if value is None:
                    return None
                try:
                    # Handle string values that might not be numeric
                    if isinstance(value, str):
                        # Remove any non-numeric characters except decimal point and minus
                        cleaned_value = ''.join(c for c in value if c.isdigit() or c in '.-')
                        if not cleaned_value or cleaned_value in ['.', '-', '.-']:
                            return None
                        return Decimal(cleaned_value)
                    else:
                        return Decimal(str(value))
                except (ValueError, TypeError, Exception):
                    return None

            # Update existing cache entry or create new one
            cache_entry, created = PriceEstimationCache.objects.update_or_create(
                cache_key=cache_key,
                defaults={
                    'estimated_price': safe_decimal_conversion(result['estimated_price']),
                    'amazon_price': safe_decimal_conversion(result['amazon_price']),
                    'dubizzle_price': safe_decimal_conversion(result['dubizzle_price']),
                    'sources_used': result['sources_used'],
                    'estimation_errors': result['errors'],
                    'expires_at': expires_at,
                }
            )
            
            if created:
                logger.debug(f"Created new cache entry for: {name}")
            else:
                logger.debug(f"Updated cache entry for: {name}")
                
        except Exception as e:
            logger.error(f"Failed to cache result for {name}: {str(e)}")
            # Don't raise exception for caching failures
